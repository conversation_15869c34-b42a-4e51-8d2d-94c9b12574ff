const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

class RuleService {
    constructor() {
        this.rulesFilePath = path.join(__dirname, 'shared-rules.json');
        this.rules = [];
        this.isInitialized = false;
    }

    async initialize() {
        console.log('规则服务初始化中...');
        try {
            await this.loadRules();
            console.log(`规则服务初始化成功，加载了 ${this.rules.length} 条共享规则`);
            this.isInitialized = true;
        } catch (error) {
            console.error('规则服务初始化失败:', error);
            throw error;
        }
    }

    // 加载规则文件
    async loadRules() {
        try {
            if (await fs.pathExists(this.rulesFilePath)) {
                const data = await fs.readJson(this.rulesFilePath);
                this.rules = Array.isArray(data) ? data : [];
                console.log(`从文件加载了 ${this.rules.length} 条规则`);
            } else {
                // 创建空的规则文件
                this.rules = [];
                await this.saveRules();
                console.log('创建了新的规则文件');
            }
        } catch (error) {
            console.error('加载规则文件失败:', error);
            this.rules = [];
            throw error;
        }
    }

    // 保存规则到文件
    async saveRules() {
        try {
            await fs.writeJson(this.rulesFilePath, this.rules, { spaces: 2 });
            console.log(`保存了 ${this.rules.length} 条规则到文件`);
        } catch (error) {
            console.error('保存规则文件失败:', error);
            throw error;
        }
    }

    // 添加共享规则
    async addSharedRule(ruleData) {
        try {
            // 验证规则数据
            if (!this.validateRule(ruleData)) {
                throw new Error('规则数据验证失败');
            }

            // 检查是否已存在相同发件人的规则
            const existingRuleIndex = this.rules.findIndex(rule => 
                rule.sender.toLowerCase() === ruleData.sender.toLowerCase()
            );

            const newRule = {
                id: uuidv4(),
                sender: ruleData.sender.toLowerCase(),
                codePattern: ruleData.codePattern,
                linkPattern: ruleData.linkPattern || null,
                example: ruleData.example,
                sample: ruleData.sample,
                createdAt: moment().toISOString(),
                updatedAt: moment().toISOString(),
                usageCount: 0,
                isActive: true
            };

            if (existingRuleIndex >= 0) {
                // 更新现有规则
                const oldRule = this.rules[existingRuleIndex];
                newRule.id = oldRule.id;
                newRule.createdAt = oldRule.createdAt;
                newRule.usageCount = oldRule.usageCount;
                
                this.rules[existingRuleIndex] = newRule;
                console.log(`更新了发件人 ${ruleData.sender} 的规则`);
            } else {
                // 添加新规则
                this.rules.push(newRule);
                console.log(`添加了发件人 ${ruleData.sender} 的新规则`);
            }

            await this.saveRules();
            return newRule.id;
        } catch (error) {
            console.error('添加共享规则失败:', error);
            throw error;
        }
    }

    // 验证规则数据
    validateRule(ruleData) {
        if (!ruleData) return false;
        
        // 必需字段检查
        if (!ruleData.sender || typeof ruleData.sender !== 'string') {
            console.error('规则验证失败: 缺少或无效的发件人');
            return false;
        }
        
        if (!ruleData.codePattern || typeof ruleData.codePattern !== 'string') {
            console.error('规则验证失败: 缺少或无效的代码模式');
            return false;
        }
        
        if (!ruleData.example || typeof ruleData.example !== 'string') {
            console.error('规则验证失败: 缺少或无效的示例');
            return false;
        }

        // 邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(ruleData.sender)) {
            console.error('规则验证失败: 发件人邮箱格式不正确');
            return false;
        }

        // 正则表达式验证
        try {
            new RegExp(ruleData.codePattern);
            if (ruleData.linkPattern) {
                new RegExp(ruleData.linkPattern);
            }
        } catch (regexError) {
            console.error('规则验证失败: 正则表达式无效', regexError);
            return false;
        }

        return true;
    }

    // 根据发件人获取规则
    async getRulesBySender(sender) {
        try {
            if (!sender) return [];
            
            const matchedRules = this.rules.filter(rule => 
                rule.isActive && rule.sender.toLowerCase() === sender.toLowerCase()
            );

            // 增加使用计数
            for (let rule of matchedRules) {
                rule.usageCount++;
            }
            
            if (matchedRules.length > 0) {
                await this.saveRules();
            }

            return matchedRules.map(rule => ({
                id: rule.id,
                sender: rule.sender,
                codePattern: rule.codePattern,
                linkPattern: rule.linkPattern,
                example: rule.example,
                createdAt: rule.createdAt,
                usageCount: rule.usageCount
            }));
        } catch (error) {
            console.error('根据发件人获取规则失败:', error);
            return [];
        }
    }

    // 获取所有共享规则
    async getAllSharedRules() {
        try {
            return this.rules
                .filter(rule => rule.isActive)
                .sort((a, b) => b.usageCount - a.usageCount) // 按使用次数排序
                .map(rule => ({
                    id: rule.id,
                    sender: rule.sender,
                    codePattern: rule.codePattern,
                    linkPattern: rule.linkPattern,
                    example: rule.example,
                    createdAt: rule.createdAt,
                    usageCount: rule.usageCount
                }));
        } catch (error) {
            console.error('获取所有共享规则失败:', error);
            return [];
        }
    }

    // 删除共享规则
    async deleteSharedRule(ruleId) {
        try {
            const ruleIndex = this.rules.findIndex(rule => rule.id === ruleId);
            
            if (ruleIndex >= 0) {
                this.rules.splice(ruleIndex, 1);
                await this.saveRules();
                console.log(`删除了规则: ${ruleId}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('删除共享规则失败:', error);
            throw error;
        }
    }

    // 软删除规则（标记为不活跃）
    async deactivateRule(ruleId) {
        try {
            const rule = this.rules.find(rule => rule.id === ruleId);
            
            if (rule) {
                rule.isActive = false;
                rule.updatedAt = moment().toISOString();
                await this.saveRules();
                console.log(`停用了规则: ${ruleId}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('停用规则失败:', error);
            throw error;
        }
    }

    // 激活规则
    async activateRule(ruleId) {
        try {
            const rule = this.rules.find(rule => rule.id === ruleId);
            
            if (rule) {
                rule.isActive = true;
                rule.updatedAt = moment().toISOString();
                await this.saveRules();
                console.log(`激活了规则: ${ruleId}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('激活规则失败:', error);
            throw error;
        }
    }

    // 获取规则统计信息
    async getStats() {
        try {
            const totalRules = this.rules.length;
            const activeRules = this.rules.filter(rule => rule.isActive).length;
            const inactiveRules = totalRules - activeRules;
            
            const senderStats = {};
            this.rules.forEach(rule => {
                const domain = rule.sender.split('@')[1];
                if (!senderStats[domain]) {
                    senderStats[domain] = 0;
                }
                senderStats[domain]++;
            });

            const topUsedRules = this.rules
                .filter(rule => rule.isActive)
                .sort((a, b) => b.usageCount - a.usageCount)
                .slice(0, 10)
                .map(rule => ({
                    sender: rule.sender,
                    usageCount: rule.usageCount,
                    createdAt: rule.createdAt
                }));

            return {
                totalRules,
                activeRules,
                inactiveRules,
                senderStats,
                topUsedRules,
                lastUpdated: moment().toISOString()
            };
        } catch (error) {
            console.error('获取规则统计失败:', error);
            return {
                totalRules: 0,
                activeRules: 0,
                inactiveRules: 0,
                senderStats: {},
                topUsedRules: [],
                lastUpdated: moment().toISOString()
            };
        }
    }

    // 获取共享规则数量
    async getSharedRulesCount() {
        return this.rules.filter(rule => rule.isActive).length;
    }

    // 清理无效规则
    async cleanupInvalidRules() {
        try {
            const validRules = [];
            let removedCount = 0;

            for (let rule of this.rules) {
                if (this.validateRule(rule)) {
                    validRules.push(rule);
                } else {
                    removedCount++;
                    console.log(`移除无效规则: ${rule.id} - ${rule.sender}`);
                }
            }

            if (removedCount > 0) {
                this.rules = validRules;
                await this.saveRules();
                console.log(`清理完成，移除了 ${removedCount} 条无效规则`);
            }

            return removedCount;
        } catch (error) {
            console.error('清理无效规则失败:', error);
            throw error;
        }
    }

    // 备份规则文件
    async backupRules() {
        try {
            const backupPath = path.join(__dirname, `shared-rules-backup-${moment().format('YYYY-MM-DD-HH-mm-ss')}.json`);
            await fs.copy(this.rulesFilePath, backupPath);
            console.log(`规则文件已备份到: ${backupPath}`);
            return backupPath;
        } catch (error) {
            console.error('备份规则文件失败:', error);
            throw error;
        }
    }

    // 从备份恢复规则
    async restoreFromBackup(backupPath) {
        try {
            if (!(await fs.pathExists(backupPath))) {
                throw new Error('备份文件不存在');
            }

            const backupData = await fs.readJson(backupPath);
            if (!Array.isArray(backupData)) {
                throw new Error('备份文件格式无效');
            }

            this.rules = backupData;
            await this.saveRules();
            console.log(`从备份恢复了 ${this.rules.length} 条规则`);
            return true;
        } catch (error) {
            console.error('从备份恢复失败:', error);
            throw error;
        }
    }

    // 获取服务状态
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            rulesCount: this.rules.length,
            activeRulesCount: this.rules.filter(rule => rule.isActive).length,
            rulesFilePath: this.rulesFilePath
        };
    }
}

module.exports = new RuleService();
