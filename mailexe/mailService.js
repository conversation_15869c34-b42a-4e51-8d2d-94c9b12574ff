const POP3Client = require('poplib');
const iconv = require('iconv-lite');
const quotedPrintable = require('quoted-printable');
const moment = require('moment');
const config = require('./config');

class MailService {
    constructor() {
        this.isConnected = false;
        this.lastConnectionTime = null;
        this.connectionPool = [];
        this.maxConnections = 3;
        this.emailCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    async initialize() {
        console.log('邮件服务初始化中...');
        try {
            // 测试连接
            await this.testConnection();
            console.log('邮件服务初始化成功');
        } catch (error) {
            console.error('邮件服务初始化失败:', error);
            throw error;
        }
    }

    // 测试POP3连接
    async testConnection() {
        return new Promise((resolve, reject) => {
            const client = new POP3Client(config.email.pop3.port, config.email.pop3.host, {
                tlserr: false,
                enabletls: true,
                debug: false
            });

            client.on('error', (err) => {
                console.error('POP3连接错误:', err);
                reject(err);
            });

            client.on('connect', () => {
                console.log('POP3连接成功');
                client.login(config.email.username, config.email.password);
            });

            client.on('login', (status, data) => {
                if (status) {
                    console.log('POP3登录成功');
                    client.quit();
                } else {
                    reject(new Error('POP3登录失败: ' + data));
                }
            });

            client.on('quit', () => {
                resolve(true);
            });
        });
    }

    // 获取指定用户的邮件
    async getEmailsForUser(userEmail, generateTime) {
        try {
            // 检查缓存
            const cacheKey = `${userEmail}_${generateTime.getTime()}`;
            const cached = this.emailCache.get(cacheKey);
            if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
                console.log('返回缓存的邮件数据');
                return cached.emails;
            }

            console.log(`获取邮件: ${userEmail}, 生成时间: ${generateTime.toISOString()}`);
            
            const emails = await this.fetchEmailsFromServer(userEmail, generateTime);
            
            // 缓存结果
            this.emailCache.set(cacheKey, {
                emails,
                timestamp: Date.now()
            });

            // 清理过期缓存
            this.cleanExpiredCache();

            return emails;
        } catch (error) {
            console.error('获取用户邮件失败:', error);
            throw error;
        }
    }

    // 从服务器获取邮件
    async fetchEmailsFromServer(userEmail, generateTime) {
        return new Promise((resolve, reject) => {
            const client = new POP3Client(config.email.pop3.port, config.email.pop3.host, {
                tlserr: false,
                enabletls: true,
                debug: false
            });

            let emailCount = 0;
            let currentEmailIndex = 0;
            const matchedEmails = [];

            client.on('error', (err) => {
                console.error('POP3错误:', err);
                reject(err);
            });

            client.on('connect', () => {
                client.login(config.email.username, config.email.password);
            });

            client.on('login', (status, data) => {
                if (status) {
                    client.list();
                } else {
                    reject(new Error('登录失败: ' + data));
                }
            });

            client.on('list', (status, msgcount, msgnumber, data, rawdata) => {
                if (status) {
                    emailCount = msgcount;
                    console.log(`邮箱中共有 ${emailCount} 封邮件`);
                    
                    if (emailCount === 0) {
                        client.quit();
                        return;
                    }
                    
                    // 从最新的邮件开始检查
                    currentEmailIndex = emailCount;
                    this.retrieveNextEmail(client, currentEmailIndex, userEmail, generateTime, matchedEmails);
                } else {
                    reject(new Error('获取邮件列表失败'));
                }
            });

            client.on('retr', (status, msgnumber, data, rawdata) => {
                if (status) {
                    try {
                        const email = this.parseEmail(rawdata, userEmail, generateTime);
                        if (email) {
                            matchedEmails.push(email);
                            console.log(`找到匹配邮件: ${email.from} - ${email.subject}`);
                        }
                    } catch (parseError) {
                        console.error('解析邮件失败:', parseError);
                    }
                }
                
                // 继续检查下一封邮件
                currentEmailIndex--;
                if (currentEmailIndex > 0) {
                    this.retrieveNextEmail(client, currentEmailIndex, userEmail, generateTime, matchedEmails);
                } else {
                    client.quit();
                }
            });

            client.on('quit', () => {
                console.log(`邮件检查完成，找到 ${matchedEmails.length} 封匹配邮件`);
                resolve(matchedEmails);
            });
        });
    }

    // 递归获取邮件
    retrieveNextEmail(client, emailIndex, userEmail, generateTime, matchedEmails) {
        // 限制检查的邮件数量，避免处理过多历史邮件
        const maxEmailsToCheck = 50;
        if (emailIndex <= Math.max(1, client.emailCount - maxEmailsToCheck)) {
            client.quit();
            return;
        }
        
        client.retr(emailIndex);
    }

    // 解析邮件内容
    parseEmail(rawData, userEmail, generateTime) {
        try {
            const emailText = rawData.toString();
            const lines = emailText.split('\r\n');
            
            let headers = {};
            let bodyLines = [];
            let isBody = false;
            let currentHeader = '';
            
            // 解析邮件头和正文
            for (let line of lines) {
                if (!isBody) {
                    if (line === '') {
                        isBody = true;
                        continue;
                    }
                    
                    if (line.startsWith(' ') || line.startsWith('\t')) {
                        // 续行
                        if (currentHeader) {
                            headers[currentHeader] += ' ' + line.trim();
                        }
                    } else {
                        const colonIndex = line.indexOf(':');
                        if (colonIndex > 0) {
                            currentHeader = line.substring(0, colonIndex).toLowerCase();
                            headers[currentHeader] = line.substring(colonIndex + 1).trim();
                        }
                    }
                } else {
                    bodyLines.push(line);
                }
            }
            
            // 检查邮件时间
            const emailDate = this.parseEmailDate(headers.date);
            if (!emailDate || emailDate <= generateTime) {
                return null; // 邮件时间早于生成时间，跳过
            }
            
            // 检查收件人是否包含用户邮箱
            const to = headers.to || '';
            const cc = headers.cc || '';
            const bcc = headers.bcc || '';
            const allRecipients = `${to} ${cc} ${bcc}`.toLowerCase();
            
            if (!allRecipients.includes(userEmail.toLowerCase())) {
                return null; // 不是发给该用户的邮件
            }
            
            // 解析正文内容
            let content = bodyLines.join('\n');
            
            // 处理编码
            if (headers['content-transfer-encoding'] === 'quoted-printable') {
                content = quotedPrintable.decode(content);
            }
            
            // 处理字符集
            const contentType = headers['content-type'] || '';
            const charsetMatch = contentType.match(/charset=([^;]+)/i);
            if (charsetMatch) {
                const charset = charsetMatch[1].replace(/['"]/g, '');
                if (charset.toLowerCase() !== 'utf-8') {
                    try {
                        content = iconv.decode(Buffer.from(content, 'binary'), charset);
                    } catch (encodeError) {
                        console.warn('字符编码转换失败:', encodeError);
                    }
                }
            }
            
            return {
                from: this.decodeHeader(headers.from || ''),
                to: this.decodeHeader(headers.to || ''),
                subject: this.decodeHeader(headers.subject || ''),
                date: emailDate.toISOString(),
                content: content.trim(),
                messageId: headers['message-id'] || '',
                contentType: contentType
            };
            
        } catch (error) {
            console.error('邮件解析错误:', error);
            return null;
        }
    }

    // 解析邮件日期
    parseEmailDate(dateString) {
        if (!dateString) return null;
        
        try {
            // 尝试多种日期格式
            const formats = [
                'ddd, DD MMM YYYY HH:mm:ss ZZ',
                'DD MMM YYYY HH:mm:ss ZZ',
                'ddd, DD MMM YYYY HH:mm:ss',
                'DD MMM YYYY HH:mm:ss'
            ];
            
            for (let format of formats) {
                const date = moment(dateString, format);
                if (date.isValid()) {
                    return date.toDate();
                }
            }
            
            // 如果都失败了，尝试JavaScript原生解析
            const date = new Date(dateString);
            if (!isNaN(date.getTime())) {
                return date;
            }
            
            return null;
        } catch (error) {
            console.error('日期解析失败:', error);
            return null;
        }
    }

    // 解码邮件头
    decodeHeader(header) {
        if (!header) return '';
        
        try {
            // 处理RFC2047编码的头部
            return header.replace(/=\?([^?]+)\?([BQ])\?([^?]+)\?=/gi, (match, charset, encoding, text) => {
                try {
                    let decoded;
                    if (encoding.toLowerCase() === 'b') {
                        decoded = Buffer.from(text, 'base64');
                    } else if (encoding.toLowerCase() === 'q') {
                        decoded = Buffer.from(quotedPrintable.decode(text.replace(/_/g, ' ')));
                    } else {
                        return match;
                    }
                    
                    return iconv.decode(decoded, charset);
                } catch (decodeError) {
                    console.warn('头部解码失败:', decodeError);
                    return match;
                }
            });
        } catch (error) {
            console.error('头部解析失败:', error);
            return header;
        }
    }

    // 清理过期缓存
    cleanExpiredCache() {
        const now = Date.now();
        const expiredKeys = [];
        
        this.emailCache.forEach((value, key) => {
            if (now - value.timestamp > this.cacheExpiry) {
                expiredKeys.push(key);
            }
        });
        
        expiredKeys.forEach(key => {
            this.emailCache.delete(key);
        });
        
        if (expiredKeys.length > 0) {
            console.log(`清理了 ${expiredKeys.length} 个过期缓存`);
        }
    }

    // 获取服务状态
    getStatus() {
        return {
            isConnected: this.isConnected,
            lastConnectionTime: this.lastConnectionTime,
            cacheSize: this.emailCache.size,
            connectionPoolSize: this.connectionPool.length
        };
    }
}

module.exports = new MailService();
