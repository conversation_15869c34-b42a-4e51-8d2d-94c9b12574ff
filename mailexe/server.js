const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cron = require('node-cron');
const { v4: uuidv4 } = require('uuid');

const mailService = require('./mailService');
const ruleService = require('./ruleService');
const config = require('./config');

const app = express();
const PORT = config.server.port || 3000;

// 中间件配置
app.use(helmet());
app.use(cors({
    origin: ['chrome-extension://*', 'moz-extension://*'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// API密钥验证中间件
const validateApiKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    if (!apiKey || apiKey !== config.api.key) {
        return res.status(401).json({ error: '无效的API密钥' });
    }
    next();
};

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100个请求
    message: { error: '请求过于频繁，请稍后再试' },
    standardHeaders: true,
    legacyHeaders: false,
});

app.use('/api/', limiter);

// 临时存储用户会话数据（内存中）
const userSessions = new Map();

// 清理过期会话的定时任务
cron.schedule('*/10 * * * *', () => {
    const now = Date.now();
    const expiredSessions = [];
    
    userSessions.forEach((session, userId) => {
        if (now - session.createdAt > config.session.maxAge) {
            expiredSessions.push(userId);
        }
    });
    
    expiredSessions.forEach(userId => {
        userSessions.delete(userId);
        console.log(`清理过期会话: ${userId}`);
    });
    
    console.log(`当前活跃会话数: ${userSessions.size}`);
});

// API路由

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        sessions: userSessions.size
    });
});

// 生成临时邮箱
app.post('/api/generate-email', validateApiKey, async (req, res) => {
    try {
        const { email, generateTime, userId } = req.body;
        
        if (!email || !generateTime || !userId) {
            return res.status(400).json({ error: '缺少必要参数' });
        }
        
        // 验证邮箱格式
        const emailRegex = /^[a-zA-Z0-9]+@shengchai\.dpdns\.org$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ error: '邮箱格式不正确' });
        }
        
        // 存储用户会话
        userSessions.set(userId, {
            email,
            generateTime: new Date(generateTime),
            createdAt: Date.now(),
            lastAccess: Date.now()
        });
        
        console.log(`新会话创建: ${userId} - ${email}`);
        
        res.json({ 
            success: true, 
            message: '临时邮箱已生成',
            sessionId: userId
        });
        
    } catch (error) {
        console.error('生成邮箱错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});

// 获取邮件
app.get('/api/get-emails', validateApiKey, async (req, res) => {
    try {
        const { userId } = req.query;
        
        if (!userId) {
            return res.status(400).json({ error: '缺少用户ID' });
        }
        
        const session = userSessions.get(userId);
        if (!session) {
            return res.status(404).json({ error: '会话不存在或已过期' });
        }
        
        // 更新最后访问时间
        session.lastAccess = Date.now();
        
        // 获取邮件
        const emails = await mailService.getEmailsForUser(session.email, session.generateTime);
        
        console.log(`获取邮件: ${userId} - 找到 ${emails.length} 封邮件`);
        
        res.json(emails);
        
    } catch (error) {
        console.error('获取邮件错误:', error);
        res.status(500).json({ error: '获取邮件失败: ' + error.message });
    }
});

// 上传共享解析规则
app.post('/api/upload-shared-rule', validateApiKey, async (req, res) => {
    try {
        const rule = req.body;
        
        if (!rule.sender || !rule.codePattern) {
            return res.status(400).json({ error: '规则数据不完整' });
        }
        
        // 验证发件人邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(rule.sender)) {
            return res.status(400).json({ error: '发件人邮箱格式不正确' });
        }
        
        const ruleId = await ruleService.addSharedRule(rule);
        
        console.log(`新共享规则添加: ${rule.sender} - ID: ${ruleId}`);
        
        res.json({ 
            success: true, 
            message: '规则已添加到共享库',
            ruleId 
        });
        
    } catch (error) {
        console.error('上传规则错误:', error);
        res.status(500).json({ error: '上传规则失败: ' + error.message });
    }
});

// 获取共享解析规则
app.get('/api/get-shared-rules', validateApiKey, async (req, res) => {
    try {
        const { sender } = req.query;
        
        let rules;
        if (sender) {
            // 获取特定发件人的规则
            rules = await ruleService.getRulesBySender(sender);
        } else {
            // 获取所有共享规则
            rules = await ruleService.getAllSharedRules();
        }
        
        res.json(rules);
        
    } catch (error) {
        console.error('获取共享规则错误:', error);
        res.status(500).json({ error: '获取共享规则失败: ' + error.message });
    }
});

// 删除共享规则（管理员功能）
app.delete('/api/shared-rule/:ruleId', validateApiKey, async (req, res) => {
    try {
        const { ruleId } = req.params;
        const { adminKey } = req.headers;
        
        // 简单的管理员验证
        if (adminKey !== config.admin.key) {
            return res.status(403).json({ error: '需要管理员权限' });
        }
        
        const success = await ruleService.deleteSharedRule(ruleId);
        
        if (success) {
            console.log(`共享规则已删除: ${ruleId}`);
            res.json({ success: true, message: '规则已删除' });
        } else {
            res.status(404).json({ error: '规则不存在' });
        }
        
    } catch (error) {
        console.error('删除规则错误:', error);
        res.status(500).json({ error: '删除规则失败: ' + error.message });
    }
});

// 获取统计信息
app.get('/api/stats', validateApiKey, async (req, res) => {
    try {
        const stats = {
            activeSessions: userSessions.size,
            totalSharedRules: await ruleService.getSharedRulesCount(),
            serverUptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            timestamp: new Date().toISOString()
        };
        
        res.json(stats);
        
    } catch (error) {
        console.error('获取统计信息错误:', error);
        res.status(500).json({ error: '获取统计信息失败' });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('未处理的错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: '接口不存在' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`临时邮箱服务器启动成功`);
    console.log(`端口: ${PORT}`);
    console.log(`时间: ${new Date().toISOString()}`);
    console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
    
    // 初始化邮件服务
    mailService.initialize().catch(error => {
        console.error('邮件服务初始化失败:', error);
    });
    
    // 初始化规则服务
    ruleService.initialize().catch(error => {
        console.error('规则服务初始化失败:', error);
    });
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});

module.exports = app;
