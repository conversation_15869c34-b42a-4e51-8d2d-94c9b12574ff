// 临时邮箱服务配置文件
const config = {
    // 服务器配置
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || '0.0.0.0',
        env: process.env.NODE_ENV || 'production'
    },

    // 邮箱配置
    email: {
        // POP3配置
        pop3: {
            host: 'pop.126.com',
            port: 995,
            secure: true // 使用SSL/TLS
        },
        
        // SMTP配置（备用）
        smtp: {
            host: 'smtp.126.com',
            port: 465,
            secure: true // 使用SSL/TLS
        },
        
        // 中转邮箱认证信息
        username: '<EMAIL>',
        password: 'TQVTv7gUbCGy9eBR', // 授权码
        
        // 邮件检查配置
        checkInterval: 5000, // 5秒检查间隔
        maxEmailsPerCheck: 50, // 每次最多检查50封邮件
        emailTimeout: 30000, // 30秒超时
        
        // 缓存配置
        cacheExpiry: 5 * 60 * 1000, // 5分钟缓存过期
        maxCacheSize: 100 // 最大缓存条目数
    },

    // API配置
    api: {
        key: 'temp-email-service-2024', // API密钥
        version: 'v1',
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15分钟
            max: 100 // 每个IP最多100个请求
        }
    },

    // 管理员配置
    admin: {
        key: 'admin-temp-email-2024', // 管理员密钥
        allowedIPs: ['127.0.0.1', '::1'] // 允许的管理员IP
    },

    // 会话配置
    session: {
        maxAge: 60 * 60 * 1000, // 1小时会话过期
        cleanupInterval: 10 * 60 * 1000, // 10分钟清理间隔
        maxSessions: 1000 // 最大并发会话数
    },

    // 域名配置
    domain: {
        allowed: ['shengchai.dpdns.org'], // 允许的域名
        default: 'shengchai.dpdns.org'
    },

    // 安全配置
    security: {
        cors: {
            origin: ['chrome-extension://*', 'moz-extension://*'],
            credentials: true
        },
        helmet: {
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"]
                }
            }
        }
    },

    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: {
            enabled: true,
            path: './logs',
            maxSize: '10m',
            maxFiles: '7d'
        },
        console: {
            enabled: true,
            colorize: true
        }
    },

    // 数据库配置（如果需要）
    database: {
        type: 'json', // 使用JSON文件存储
        path: './data',
        backup: {
            enabled: true,
            interval: 24 * 60 * 60 * 1000, // 24小时备份间隔
            maxBackups: 7 // 保留7个备份
        }
    },

    // 性能配置
    performance: {
        // 内存限制
        memoryLimit: 512 * 1024 * 1024, // 512MB
        
        // 并发限制
        maxConcurrentConnections: 10,
        
        // 超时配置
        requestTimeout: 30000, // 30秒请求超时
        connectionTimeout: 10000, // 10秒连接超时
        
        // 队列配置
        queue: {
            enabled: true,
            maxSize: 100,
            concurrency: 3
        }
    },

    // 监控配置
    monitoring: {
        enabled: true,
        metrics: {
            enabled: true,
            interval: 60000 // 1分钟收集间隔
        },
        health: {
            enabled: true,
            endpoint: '/api/health'
        }
    },

    // 开发配置
    development: {
        debug: process.env.NODE_ENV === 'development',
        mockEmails: false, // 是否使用模拟邮件
        verbose: false // 详细日志
    }
};

// 环境特定配置覆盖
if (config.server.env === 'development') {
    config.logging.level = 'debug';
    config.development.debug = true;
    config.development.verbose = true;
    config.api.rateLimit.max = 1000; // 开发环境放宽限制
}

if (config.server.env === 'production') {
    config.logging.level = 'info';
    config.development.debug = false;
    config.development.verbose = false;
}

// 配置验证函数
function validateConfig() {
    const errors = [];
    
    // 验证必需的配置项
    if (!config.email.username) {
        errors.push('邮箱用户名未配置');
    }
    
    if (!config.email.password) {
        errors.push('邮箱密码未配置');
    }
    
    if (!config.api.key) {
        errors.push('API密钥未配置');
    }
    
    if (!config.domain.allowed || config.domain.allowed.length === 0) {
        errors.push('允许的域名未配置');
    }
    
    // 验证端口号
    if (config.server.port < 1 || config.server.port > 65535) {
        errors.push('服务器端口号无效');
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(config.email.username)) {
        errors.push('邮箱用户名格式无效');
    }
    
    if (errors.length > 0) {
        throw new Error('配置验证失败:\n' + errors.join('\n'));
    }
    
    return true;
}

// 获取配置信息（隐藏敏感信息）
function getSafeConfig() {
    const safeConfig = JSON.parse(JSON.stringify(config));
    
    // 隐藏敏感信息
    safeConfig.email.password = '***';
    safeConfig.api.key = '***';
    safeConfig.admin.key = '***';
    
    return safeConfig;
}

// 更新配置
function updateConfig(newConfig) {
    Object.assign(config, newConfig);
    validateConfig();
}

// 导出配置和工具函数
module.exports = {
    ...config,
    validateConfig,
    getSafeConfig,
    updateConfig
};
