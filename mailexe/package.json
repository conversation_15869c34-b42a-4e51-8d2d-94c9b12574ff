{"name": "temp-email-server", "version": "1.0.0", "description": "临时邮箱服务后端程序", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["email", "temporary", "pop3", "mail-service"], "author": "TempEmailService", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "poplib": "^0.1.7", "iconv-lite": "^0.6.3", "quoted-printable": "^1.0.1", "utf8": "^3.0.0", "node-cron": "^3.0.2", "fs-extra": "^11.1.1", "uuid": "^9.0.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}