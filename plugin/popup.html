<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱服务</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>临时邮箱服务</h1>
        </header>
        
        <main>
            <!-- 邮箱生成区域 -->
            <section class="email-generator">
                <div class="current-email" id="currentEmail">
                    <span class="label">当前临时邮箱：</span>
                    <span class="email-address" id="emailAddress">未生成</span>
                    <button class="copy-btn" id="copyBtn" style="display: none;">复制</button>
                </div>
                <button class="generate-btn" id="generateBtn">生成新的临时邮箱</button>
                <div class="status" id="status"></div>
            </section>

            <!-- 邮件显示区域 -->
            <section class="email-display">
                <h3>收到的邮件</h3>
                <div class="email-list" id="emailList">
                    <div class="no-emails">暂无邮件</div>
                </div>
                <button class="refresh-btn" id="refreshBtn" style="display: none;">刷新邮件</button>
            </section>

            <!-- 解析结果区域 -->
            <section class="parse-results">
                <h3>解析结果</h3>
                <div class="verification-code" id="verificationCode">
                    <span class="label">验证码：</span>
                    <span class="code" id="codeValue">-</span>
                    <button class="copy-btn" id="copyCodeBtn" style="display: none;">复制</button>
                </div>
                <div class="verification-link" id="verificationLink">
                    <span class="label">链接：</span>
                    <a href="#" id="linkValue" target="_blank" style="display: none;">-</a>
                    <button class="copy-btn" id="copyLinkBtn" style="display: none;">复制</button>
                </div>
            </section>

            <!-- 解析规则管理 -->
            <section class="rule-management">
                <h3>解析规则管理</h3>
                <button class="toggle-btn" id="toggleRuleForm">添加新规则</button>
                
                <div class="rule-form" id="ruleForm" style="display: none;">
                    <div class="form-group">
                        <label for="senderEmail">发件人邮箱：</label>
                        <input type="email" id="senderEmail" placeholder="例如：<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="emailSample">邮件示例：</label>
                        <textarea id="emailSample" placeholder="粘贴完整的邮件内容示例"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="codeExample">验证码示例：</label>
                        <input type="text" id="codeExample" placeholder="例如：123456">
                    </div>
                    <div class="form-actions">
                        <button class="save-local-btn" id="saveLocalBtn">保存到本地</button>
                        <button class="share-btn" id="shareBtn">共享给所有用户</button>
                    </div>
                </div>
                
                <div class="rule-list" id="ruleList">
                    <h4>本地规则</h4>
                    <div class="local-rules" id="localRules">暂无本地规则</div>
                </div>
            </section>
        </main>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
