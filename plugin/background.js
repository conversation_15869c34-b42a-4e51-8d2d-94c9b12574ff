// 临时邮箱服务后台脚本
class BackgroundService {
    constructor() {
        this.init();
    }

    init() {
        // 监听插件安装事件
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('临时邮箱服务插件已安装');
                this.initializeStorage();
            } else if (details.reason === 'update') {
                console.log('临时邮箱服务插件已更新');
            }
        });

        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听存储变化
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    // 初始化存储
    async initializeStorage() {
        try {
            const result = await chrome.storage.local.get(['localRules', 'settings']);
            
            if (!result.localRules) {
                await chrome.storage.local.set({ localRules: [] });
            }
            
            if (!result.settings) {
                await chrome.storage.local.set({ 
                    settings: {
                        domain: 'shengchai.dpdns.org',
                        apiBaseUrl: 'https://1.12.224.176/api',
                        autoRefresh: true,
                        refreshInterval: 5000
                    }
                });
            }
        } catch (error) {
            console.error('初始化存储失败:', error);
        }
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'generateUserId':
                    const userId = this.generateUserId();
                    sendResponse({ success: true, userId });
                    break;
                    
                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ success: true, settings });
                    break;
                    
                case 'updateSettings':
                    await this.updateSettings(request.settings);
                    sendResponse({ success: true });
                    break;
                    
                case 'clearAllData':
                    await this.clearAllData();
                    sendResponse({ success: true });
                    break;
                    
                case 'exportRules':
                    const rules = await this.exportRules();
                    sendResponse({ success: true, rules });
                    break;
                    
                case 'importRules':
                    await this.importRules(request.rules);
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 生成用户ID
    generateUserId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `${timestamp}_${random}`;
    }

    // 获取设置
    async getSettings() {
        try {
            const result = await chrome.storage.local.get(['settings']);
            return result.settings || {};
        } catch (error) {
            console.error('获取设置失败:', error);
            return {};
        }
    }

    // 更新设置
    async updateSettings(newSettings) {
        try {
            const currentSettings = await this.getSettings();
            const updatedSettings = { ...currentSettings, ...newSettings };
            await chrome.storage.local.set({ settings: updatedSettings });
        } catch (error) {
            console.error('更新设置失败:', error);
            throw error;
        }
    }

    // 清空所有数据
    async clearAllData() {
        try {
            await chrome.storage.local.clear();
            await this.initializeStorage();
        } catch (error) {
            console.error('清空数据失败:', error);
            throw error;
        }
    }

    // 导出规则
    async exportRules() {
        try {
            const result = await chrome.storage.local.get(['localRules']);
            return result.localRules || [];
        } catch (error) {
            console.error('导出规则失败:', error);
            throw error;
        }
    }

    // 导入规则
    async importRules(rules) {
        try {
            if (!Array.isArray(rules)) {
                throw new Error('规则数据格式错误');
            }
            
            const currentRules = await this.exportRules();
            const mergedRules = [...currentRules];
            
            // 合并规则，避免重复
            rules.forEach(newRule => {
                const exists = mergedRules.some(rule => rule.sender === newRule.sender);
                if (!exists) {
                    mergedRules.push(newRule);
                }
            });
            
            await chrome.storage.local.set({ localRules: mergedRules });
        } catch (error) {
            console.error('导入规则失败:', error);
            throw error;
        }
    }

    // 处理存储变化
    handleStorageChange(changes, namespace) {
        if (namespace === 'local') {
            for (const key in changes) {
                const change = changes[key];
                console.log(`存储变化 - ${key}:`, {
                    oldValue: change.oldValue,
                    newValue: change.newValue
                });
                
                // 可以在这里添加特定的处理逻辑
                if (key === 'localRules') {
                    this.onRulesChanged(change.newValue);
                } else if (key === 'settings') {
                    this.onSettingsChanged(change.newValue);
                }
            }
        }
    }

    // 规则变化处理
    onRulesChanged(newRules) {
        console.log('本地规则已更新，数量:', newRules ? newRules.length : 0);
        
        // 通知所有打开的popup页面
        chrome.runtime.sendMessage({
            type: 'rulesUpdated',
            rules: newRules
        }).catch(() => {
            // 忽略没有接收者的错误
        });
    }

    // 设置变化处理
    onSettingsChanged(newSettings) {
        console.log('设置已更新:', newSettings);
        
        // 通知所有打开的popup页面
        chrome.runtime.sendMessage({
            type: 'settingsUpdated',
            settings: newSettings
        }).catch(() => {
            // 忽略没有接收者的错误
        });
    }

    // 定期清理过期数据
    async cleanupExpiredData() {
        try {
            const result = await chrome.storage.local.get(['tempData']);
            if (result.tempData) {
                const now = Date.now();
                const cleanedData = {};
                
                for (const key in result.tempData) {
                    const data = result.tempData[key];
                    // 清理超过1小时的临时数据
                    if (data.timestamp && (now - data.timestamp) < 3600000) {
                        cleanedData[key] = data;
                    }
                }
                
                await chrome.storage.local.set({ tempData: cleanedData });
            }
        } catch (error) {
            console.error('清理过期数据失败:', error);
        }
    }
}

// 初始化后台服务
const backgroundService = new BackgroundService();

// 定期清理过期数据（每30分钟）
setInterval(() => {
    backgroundService.cleanupExpiredData();
}, 30 * 60 * 1000);
