/* 临时邮箱服务插件样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 500px;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    padding: 16px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

header h1 {
    font-size: 18px;
    color: #007bff;
    font-weight: 600;
}

/* 通用按钮样式 */
button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    outline: none;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

button:active {
    transform: translateY(0);
}

/* 主要按钮 */
.generate-btn, .refresh-btn, .share-btn {
    background-color: #007bff;
    color: white;
    width: 100%;
    padding: 10px;
    font-size: 14px;
    margin-bottom: 10px;
}

.generate-btn:hover, .refresh-btn:hover, .share-btn:hover {
    background-color: #0056b3;
}

/* 次要按钮 */
.copy-btn, .save-local-btn, .toggle-btn {
    background-color: #6c757d;
    color: white;
    font-size: 11px;
    padding: 4px 8px;
    margin-left: 8px;
}

.copy-btn:hover, .save-local-btn:hover, .toggle-btn:hover {
    background-color: #545b62;
}

/* 删除按钮 */
.delete-rule-btn {
    background-color: #dc3545;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
}

.delete-rule-btn:hover {
    background-color: #c82333;
}

/* 区域样式 */
section {
    margin-bottom: 20px;
    background: white;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

section h3 {
    font-size: 14px;
    color: #495057;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e9ecef;
}

/* 邮箱生成区域 */
.current-email {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.current-email .label {
    font-weight: 500;
    color: #6c757d;
    margin-right: 8px;
    white-space: nowrap;
}

.email-address {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #007bff;
    word-break: break-all;
}

/* 状态显示 */
.status {
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    margin-top: 8px;
}

.status.loading {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 邮件列表 */
.email-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-emails {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

.email-item {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fff;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.sender {
    font-weight: 500;
    color: #007bff;
    font-size: 12px;
}

.time {
    font-size: 10px;
    color: #6c757d;
}

.email-subject {
    font-weight: 500;
    margin-bottom: 4px;
    font-size: 13px;
    color: #495057;
}

.email-content {
    font-size: 11px;
    color: #6c757d;
    max-height: 60px;
    overflow: hidden;
    line-height: 1.4;
}

/* 解析结果 */
.verification-code, .verification-link {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.verification-code .label, .verification-link .label {
    font-weight: 500;
    color: #6c757d;
    margin-right: 8px;
    white-space: nowrap;
}

.code {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #28a745;
}

#linkValue {
    flex: 1;
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
    word-break: break-all;
}

#linkValue:hover {
    text-decoration: underline;
}

/* 规则管理 */
.rule-form {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
    background-color: #f8f9fa;
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #495057;
    font-size: 12px;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
    font-family: inherit;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
    max-height: 120px;
}

.form-actions {
    display: flex;
    gap: 8px;
}

.form-actions button {
    flex: 1;
}

/* 规则列表 */
.rule-list h4 {
    font-size: 12px;
    color: #6c757d;
    margin: 10px 0 8px 0;
}

.local-rules {
    font-size: 12px;
    color: #6c757d;
}

.rule-item {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 6px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rule-sender {
    font-weight: 500;
    color: #007bff;
    font-size: 11px;
}

.rule-example {
    font-size: 10px;
    color: #6c757d;
    margin-top: 2px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 350px) {
    body {
        width: 320px;
    }
    
    .container {
        padding: 12px;
    }
    
    .current-email {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .current-email .label {
        margin-bottom: 4px;
    }
}
